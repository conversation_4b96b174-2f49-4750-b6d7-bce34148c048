<?php
/**
 * Gestionnaire de contenu
 * Charge et fournit le contenu depuis les fichiers PHP
 */
class ContentManager {
    private $lang;
    private $content;

    /**
     * Constructeur
     *
     * @param string $lang Code de langue
     */
    public function __construct($lang) {
        $this->lang = $lang;
        $this->loadContent();
    }

    /**
     * Charge le contenu pour la langue courante
     */
    private function loadContent() {
        $config = json_decode(file_get_contents(ROOT_DIR . '/data/config/' . $this->lang . '.json'), true);
        $langFile = ROOT_DIR . '/data/lang/' . $this->lang . '.php';

        if (file_exists($langFile)) {
            $this->content = require $langFile;
        } else {
            // Fichier de langue non trouvé
            error_log('Fichier de langue non trouvé: ' . $langFile);
            $this->content = [];
        }
    }

    /**
     * Récupère une valeur de contenu par sa clé
     * Supporte la notation par points pour accéder aux propriétés imbriquées
     *
     * @param string $key Clé du contenu (ex: "home.hero.title")
     * @param mixed $default Valeur par défaut si la clé n'existe pas
     * @return mixed Valeur du contenu ou valeur par défaut
     */
    public function get($key, $default = '') {
        $keys = explode('.', $key);
        $value = $this->content;

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }

        return $value;
    }

    /**
     * Vérifie si une clé de contenu existe
     *
     * @param string $key Clé du contenu
     * @return bool True si la clé existe, false sinon
     */
    public function has($key) {
        $keys = explode('.', $key);
        $value = $this->content;

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return false;
            }
            $value = $value[$k];
        }

        return true;
    }

    /**
     * Récupère tout le contenu
     *
     * @return array Contenu complet
     */
    public function getAll() {
        return $this->content;
    }

    /**
     * Change la langue et recharge le contenu
     *
     * @param string $lang Nouveau code de langue
     */
    public function setLanguage($lang) {
        $this->lang = $lang;
        $this->loadContent();
    }
}
