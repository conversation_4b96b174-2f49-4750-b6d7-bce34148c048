<!-- Hero Section -->
<section class="page-header bg-primary text-white">
    <div class="container">
        <div class="row py-5">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3"><?php echo $loanDetails['title']; ?></h1>
                <p class="lead mb-0"><?php echo $loanDetails['subtitle']; ?></p>
            </div>
        </div>
    </div>
</section>

<!-- Loan Details Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="mb-5">
                    <h2 class="mb-4"><?php echo $loanDetails['intro']['title']; ?></h2>
                    <p class="lead mb-4"><?php echo $loanDetails['intro']['content']; ?></p>
                    
                    <?php if (isset($loanDetails['intro']['points'])): ?>
                        <ul class="list-unstyled">
                            <?php foreach ($loanDetails['intro']['points'] as $point): ?>
                                <li class="mb-3">
                                    <div class="d-flex">
                                        <div class="d-flex align-items-center justify-content-center me-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="text-success" viewBox="0 0 512 512">
                                                <path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <h5 class="mb-1"><?php echo $point['title']; ?></h5>
                                            <p class="mb-0"><?php echo $point['description']; ?></p>
                                        </div>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
                
                <?php if (isset($loanDetails['features'])): ?>
                    <div class="mb-5">
                        <h2 class="mb-4"><?php echo $loanDetails['features']['title']; ?></h2>
                        
                        <div class="row">
                            <?php foreach ($loanDetails['features']['items'] as $feature): ?>
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <div class="card-body p-4">
                                            <h4 class="card-title mb-3"><?php echo $feature['title']; ?></h4>
                                            <p class="card-text"><?php echo $feature['description']; ?></p>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($loanDetails['eligibility'])): ?>
                    <div class="mb-5">
                        <h2 class="mb-4"><?php echo $loanDetails['eligibility']['title']; ?></h2>
                        <p class="mb-4"><?php echo $loanDetails['eligibility']['intro']; ?></p>
                        
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-body p-4">
                                <h4 class="card-title mb-4"><?php echo $loans['criteria-title']; ?></h4>
                                <ul class="list-group list-group-flush">
                                    <?php foreach ($loanDetails['eligibility']['criteria'] as $criterion): ?>
                                        <li class="list-group-item px-0">
                                            <div class="d-flex">
                                                <div class="d-flex align-items-center justify-content-center me-3">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="text-success" viewBox="0 0 512 512">
                                                        <path d="M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z"/>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <?php echo $criterion; ?>
                                                </div>
                                            </div>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                        
                        <?php if (isset($loanDetails['eligibility']['documents'])): ?>
                            <div class="card border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <h4 class="card-title mb-4"><?php echo $loans['documents-title']; ?></h4>
                                    <ul class="list-group list-group-flush">
                                        <?php foreach ($loanDetails['eligibility']['documents'] as $document): ?>
                                            <li class="list-group-item px-0">
                                                <div class="d-flex">
                                                    <div class="me-3">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="text-primary" viewBox="0 0 512 512">
                                                            <path d="M64 0C28.7 0 0 28.7 0 64L0 448c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-288-128 0c-17.7 0-32-14.3-32-32L224 0 64 0zM256 0l0 128 128 0L256 0zM112 256l160 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-160 0c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64l160 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-160 0c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64l160 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-160 0c-8.8 0-16-7.2-16-16s7.2-16 16-16z"/>
                                                        </svg>
                                                    </div>
                                                    <div>
                                                        <?php echo $document; ?>
                                                    </div>
                                                </div>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($loans['calculator'])): ?>
                    <div class="mb-5">
                        <h2 class="mb-4"><?php echo $loans['calculator']['title']; ?></h2>
                        <p class="mb-4"><?php echo $loans['calculator']['intro']; ?></p>
                        
                        <div class="card border-0 shadow-sm">
                            <div class="card-body p-4">
                                <form id="loanCalculator" class="needs-validation" novalidate>
                                    <div class="row mb-4">
                                        <div class="col-md-6 mb-3 mb-md-0">
                                            <label for="loanAmount" class="form-label"><?php echo $loans['calculator']['amountLabel']; ?></label>
                                            <input type="number" class="form-control" id="loanAmount" min="1000" max="1000000" step="1000" value="10000" placeholder="<?php echo $loans['calculator']['amountPlaceholder']; ?>" required>
                                            <div class="invalid-feedback">
                                                <?php echo $loans['calculator']['amountError']; ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="loanDuration" class="form-label"><?php echo $loans['calculator']['durationLabel']; ?></label>
                                            <input type="number" class="form-control" id="loanDuration" min="12" max="360" step="12" value="60" placeholder="<?php echo $loans['calculator']['durationValue']; ?>" required>
                                            <div class="invalid-feedback">
                                                <?php echo $loans['calculator']['durationError']; ?>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-4">
                                        <div class="col-md-6 mb-3 mb-md-0">
                                            <label for="interestRate" class="form-label"><?php echo $loans['calculator']['interestRateLabel']; ?></label>
                                            <input type="number" class="form-control" id="interestRate" min="0.1" max="20" step="0.1" value="3.5" placeholder="<?php echo $loans['calculator']['interestRateValue']; ?>" required>
                                            <div class="invalid-feedback">
                                                <?php echo $loans['calculator']['interestRateError']; ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6 d-flex align-items-end">
                                            <button type="button" class="btn btn-primary w-100" id="calculateBtn"><?php echo $loans['calculator']['calculateButton']; ?></button>
                                        </div>
                                    </div>
                                    
                                    <div id="calculationResult" class="mt-4 p-4 bg-light rounded d-none">
                                        <h5 class="mb-3"><?php echo $loans['calculator']['calculationResult']; ?></h5>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <p class="mb-1 text-muted"><?php echo $loans['calculator']['monthlyPayment']; ?></p>
                                                <h4 id="monthlyPayment">- €</h4>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <p class="mb-1 text-muted"><?php echo $loans['calculator']['totalCost']; ?></p>
                                                <h4 id="totalCost">- €</h4>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <p class="mb-1 text-muted"><?php echo $loans['calculator']['totalAmount']; ?></p>
                                                <h4 id="totalAmount">- €</h4>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="mb-1 text-muted"><?php echo $loans['calculator']['taeg']; ?></p>
                                                <h4 id="taeg">- %</h4>
                                            </div>
                                        </div>
                                        <p class="mt-3 mb-0 small text-muted"><?php echo $loans['calculator']['note']; ?></p>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($loans['faq'])): ?>
                    <div class="mb-5">
                        <h2 class="mb-4"><?php echo $loans['faq']['title']; ?></h2>
                        
                        <div class="accordion" id="loanFaqAccordion">
                            <?php foreach ($loans['faq']['items'] as $index => $faq): ?>
                                <div class="accordion-item mb-3 border-0 shadow-sm">
                                    <h3 class="accordion-header" id="loanHeading<?php echo $index; ?>">
                                        <button class="accordion-button <?php echo $index > 0 ? 'collapsed' : ''; ?>" type="button" data-bs-toggle="collapse" data-bs-target="#loanCollapse<?php echo $index; ?>" aria-expanded="<?php echo $index === 0 ? 'true' : 'false'; ?>" aria-controls="loanCollapse<?php echo $index; ?>">
                                            <?php echo $faq['question']; ?>
                                        </button>
                                    </h3>
                                    <div id="loanCollapse<?php echo $index; ?>" class="accordion-collapse collapse <?php echo $index === 0 ? 'show' : ''; ?>" aria-labelledby="loanHeading<?php echo $index; ?>" data-bs-parent="#loanFaqAccordion">
                                        <div class="accordion-body">
                                            <?php echo $faq['answer']; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="col-lg-4">
                <!-- Sidebar -->
                <div class="sticky-top" style="top: 100px;">
                    <!-- Quick Apply Card -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-body p-4">
                            <h4 class="card-title mb-4"><?php echo $loanDetails['applyCta']; ?></h4>
                            <p class="mb-4"><?php echo $loanDetails['subtitle'].'.</br> '.$loans['sidebar']['applyCard']['text']; ?></p>
                            <a href="<?php echo $this->languageManager->getLocalizedUrl('/apply/' . $loanType); ?>" class="btn btn-primary w-100 mb-3">
                                <?php echo $loans['sidebar']['applyCard']['button']; ?>
                            </a>
                            <p class="small text-muted mb-0"><?php echo $loans['sidebar']['applyCard']['note']; ?></p>
                        </div>
                    </div>
                    
                    <!-- Key Information Card -->
                    <?php if (isset($loanDetails['keyInfo'])): ?>
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-body p-4">
                                <h4 class="card-title mb-4"><?php echo $loanDetails['keyInfo']['title']; ?></h4>
                                <ul class="list-unstyled mb-0">
                                    <?php foreach ($loanDetails['keyInfo']['items'] as $info): ?>
                                        <li class="mb-3 pb-3 border-bottom">
                                            <div class="d-flex justify-content-between">
                                                <span class="text-muted"><?php echo $info['label']; ?></span>
                                                <span class="fw-bold"><?php echo $info['value']; ?></span>
                                            </div>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Contact Card -->
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h4 class="card-title mb-4"><?php echo $loans['sidebar']['contactCard']['title']; ?></h4>
                            <p class="mb-4"><?php echo $loans['sidebar']['contactCard']['text']; ?></p>
                            <ul class="list-unstyled mb-4">
                                <li class="mb-3">
                                    <div class="d-flex">
                                        <div class="text-primary me-3">
                                            <svg class="me-2" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 512 512">
                                                <path d="M164.9 24.6c-7.7-18.6-28-28.5-47.4-23.2l-88 24C12.1 30.2 0 46 0 64C0 311.4 200.6 512 448 512c18 0 33.8-12.1 38.6-29.5l24-88c5.3-19.4-4.6-39.7-23.2-47.4l-96-40c-16.3-6.8-35.2-2.1-46.3 11.6L304.7 368C234.3 334.7 177.3 277.7 144 207.3L193.3 167c13.7-11.2 18.4-30 11.6-46.3l-40-96z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <?php echo $global['footer']['phone']; ?>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="d-flex">
                                        <div class="text-primary me-3">
                                            <svg class="me-2" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 512 512">
                                                <path d="M48 64C21.5 64 0 85.5 0 112c0 15.1 7.1 29.3 19.2 38.4L236.8 313.6c11.4 8.5 27 8.5 38.4 0L492.8 150.4c12.1-9.1 19.2-23.3 19.2-38.4c0-26.5-21.5-48-48-48L48 64zM0 176L0 384c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-208L294.4 339.2c-22.8 17.1-54 17.1-76.8 0L0 176z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <?php echo $global['footer']['email']; ?>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                            <a href="<?php echo $this->languageManager->getLocalizedUrl('/contact'); ?>" class="btn btn-outline-primary w-100">
                                <?php echo $loans['sidebar']['contactCard']['button']; ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Loans Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-3"><?php echo $loans['relatedLoans']['title']; ?></h2>
                <p class="lead"><?php echo $loans['relatedLoans']['subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row">
            <?php 
            $count = 0;
            foreach ($loans['loanTypes'] as $relatedLoan):
                if ($relatedLoan['id'] !== $loanType && $count < 3):
                    $count++;
            ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <picture>
                            <source srcset="/assets/images/<?php echo $relatedLoan['image']; ?>.avif" type="image/avif">
                            <source srcset="/assets/images/<?php echo $relatedLoan['image']; ?>.webp" type="image/webp">
                            <img src="/assets/images/<?php echo $relatedLoan['image']; ?>.jpg" class="card-img-top" alt="<?php echo $loan['title']; ?>" loading="lazy">
                        </picture>
                        <div class="card-body p-4">
                            <h4 class="card-title mb-3"><?php echo $relatedLoan['title']; ?></h4>
                            <p class="card-text mb-4"><?php echo $relatedLoan['shortDescription']; ?></p>
                            <a href="<?php echo $this->languageManager->getLocalizedUrl('/loans/' . $relatedLoan['id']); ?>" class="btn btn-outline-primary">
                                <?php echo $relatedLoan['cta']; ?>
                            </a>
                        </div>
                    </div>
                </div>
            <?php 
                endif;
            endforeach; 
            ?>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h2 class="mb-3"><?php echo $content['cta']['title']; ?></h2>
                <p class="lead mb-4"><?php echo $content['cta']['subtitle']; ?></p>
                <a href="<?php echo $this->languageManager->getLocalizedUrl('/apply/' . $loanType); ?>" class="btn btn-light btn-lg mb-3">
                    <?php echo $content['cta']['button']; ?>
                </a>
                <p class="mb-0"><?php echo $content['cta']['contact']; ?></p>
            </div>
        </div>
    </div>
</section>

<!-- Loan Calculator Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculateBtn');
    if (calculateBtn) {
        calculateBtn.addEventListener('click', function(event) {
            const form = document.querySelector('.needs-validation');
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                form.classList.add('was-validated');
                return; // Arrête l'exécution si le formulaire n'est pas valide
            }
            const amount = parseFloat(document.getElementById('loanAmount').value);
            const duration = parseInt(document.getElementById('loanDuration').value);
            const rate = parseFloat(document.getElementById('interestRate').value);
            
            // Calcul de la mensualité (formule de l'annuité constante)
            const monthlyRate = rate / 100 / 12;
            const monthlyPayment = amount * monthlyRate * Math.pow(1 + monthlyRate, duration) / (Math.pow(1 + monthlyRate, duration) - 1);
            
            // Calcul du coût total du crédit
            const totalAmount = monthlyPayment * duration;
            const totalCost = totalAmount - amount;
            
            // Calcul du TAEG (approximation)
            const taeg = (Math.pow(1 + monthlyRate, 12) - 1) * 100;
            
            // Affichage des résultats
            document.getElementById('monthlyPayment').textContent = monthlyPayment.toFixed(2) + ' €';
            document.getElementById('totalCost').textContent = totalCost.toFixed(2) + ' €';
            document.getElementById('totalAmount').textContent = totalAmount.toFixed(2) + ' €';
            document.getElementById('taeg').textContent = taeg.toFixed(2) + ' %';
            
            document.getElementById('calculationResult').classList.remove('d-none');
        });
    }
});
</script>
