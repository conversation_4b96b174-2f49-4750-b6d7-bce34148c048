<?php
/**
 * Classe PHPMailer simplifiée pour l'envoi d'emails
 * Utilise la fonction mail() de PHP
 */
class PHPMailer {
    private $to;
    private $subject;
    private $message;
    private $headers = [];
    private $attachments = [];
    
    /**
     * Constructeur
     */
    public function __construct() {
        // Définir les en-têtes par défaut
        $this->addHeader('MIME-Version', '1.0');
        $this->addHeader('Content-Type', 'text/html; charset=UTF-8');
    }
    
    /**
     * Définit le destinataire
     * 
     * @param string $email Adresse email du destinataire
     * @param string $name Nom du destinataire (optionnel)
     * @return PHPMailer
     */
    public function setTo($email, $name = '') {
        $this->to = $name ? "$name <$email>" : $email;
        return $this;
    }
    
    /**
     * Définit l'expéditeur
     * 
     * @param string $email Adresse email de l'expéditeur
     * @param string $name Nom de l'expéditeur (optionnel)
     * @return PHPMailer
     */
    public function setFrom($email, $name = '') {
        $this->addHeader('From', $name ? "$name <$email>" : $email);
        return $this;
    }
    
    /**
     * Définit l'adresse de réponse
     * 
     * @param string $email Adresse email de réponse
     * @param string $name Nom associé à l'adresse de réponse (optionnel)
     * @return PHPMailer
     */
    public function setReplyTo($email, $name = '') {
        $this->addHeader('Reply-To', $name ? "$name <$email>" : $email);
        return $this;
    }
    
    /**
     * Définit le sujet de l'email
     * 
     * @param string $subject Sujet de l'email
     * @return PHPMailer
     */
    public function setSubject($subject) {
        $this->subject = $subject;
        return $this;
    }
    
    /**
     * Définit le corps de l'email
     * 
     * @param string $message Corps de l'email (HTML)
     * @return PHPMailer
     */
    public function setBody($message) {
        $this->message = $message;
        return $this;
    }
    
    /**
     * Ajoute un en-tête personnalisé
     * 
     * @param string $name Nom de l'en-tête
     * @param string $value Valeur de l'en-tête
     * @return PHPMailer
     */
    public function addHeader($name, $value) {
        $this->headers[$name] = $value;
        return $this;
    }
    
    /**
     * Ajoute une pièce jointe
     * 
     * @param string $path Chemin vers le fichier à joindre
     * @return PHPMailer
     */
    public function addAttachment($path) {
        if (file_exists($path)) {
            $this->attachments[] = $path;
        }
        return $this;
    }
    
    /**
     * Envoie l'email
     * 
     * @return bool Succès ou échec de l'envoi
     */
    public function send() {
        // Vérifier que les informations essentielles sont présentes
        if (empty($this->to) || empty($this->subject) || empty($this->message)) {
            return false;
        }
        
        // Préparer les en-têtes
        $headers = '';
        foreach ($this->headers as $name => $value) {
            $headers .= "$name: $value\r\n";
        }
        
        // Envoyer l'email
        return mail($this->to, $this->subject, $this->message, $headers);
    }
    
    /**
     * Crée un email de contact
     * 
     * @param array $data Données du formulaire de contact
     * @return PHPMailer
     */
    public static function createContactEmail($data) {
        $mailer = new self();
        
        // Destinataire (adresse de l'entreprise)
        $mailer->setTo('<EMAIL>', 'FinancExpert');
        
        // Expéditeur (personne qui a rempli le formulaire)
        $mailer->setFrom('<EMAIL>', 'Formulaire de contact');
        $mailer->setReplyTo($data['email'], $data['first_name'] . ' ' . $data['last_name']);
        
        // Sujet
        $mailer->setSubject('Nouveau message de contact: ' . $data['subject']);
        
        // Corps du message
        $message = "
        <html>
        <head>
            <title>Nouveau message de contact</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                h1 { color: #0056b3; }
                .info { margin-bottom: 20px; }
                .label { font-weight: bold; }
            </style>
        </head>
        <body>
            <div class='container'>
                <h1>Nouveau message de contact</h1>
                <div class='info'>
                    <p><span class='label'>Nom:</span> {$data['last_name']}</p>
                    <p><span class='label'>Prénom:</span> {$data['first_name']}</p>
                    <p><span class='label'>Email:</span> {$data['email']}</p>
                    " . (isset($data['phone']) && !empty($data['phone']) ? "<p><span class='label'>Téléphone:</span> {$data['phone']}</p>" : "") . "
                    <p><span class='label'>Pays:</span> {$data['country']}</p>
                    <p><span class='label'>Sujet:</span> {$data['subject']}</p>
                </div>
                <div class='message'>
                    <p><span class='label'>Message:</span></p>
                    <p>" . nl2br(htmlspecialchars($data['message'])) . "</p>
                </div>
            </div>
        </body>
        </html>
        ";
        
        $mailer->setBody($message);
        
        return $mailer;
    }
    
    /**
     * Crée un email de demande de prêt
     * 
     * @param array $data Données du formulaire de demande de prêt
     * @return PHPMailer
     */
    public static function createLoanApplicationEmail($data) {
        $mailer = new self();
        
        // Destinataire (adresse de l'entreprise)
        $mailer->setTo('<EMAIL>', 'FinancExpert - Service des prêts');
        
        // Expéditeur (système)
        $mailer->setFrom('<EMAIL>', 'Formulaire de demande de prêt');
        $mailer->setReplyTo($data['email'], $data['first_name'] . ' ' . $data['last_name']);
        
        // Sujet
        $mailer->setSubject('Nouvelle demande de prêt: ' . $data['loan_type']);
        
        // Corps du message
        $message = "
        <html>
        <head>
            <title>Nouvelle demande de prêt</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                h1 { color: #0056b3; }
                h2 { color: #0056b3; font-size: 18px; margin-top: 30px; }
                .info { margin-bottom: 20px; }
                .label { font-weight: bold; }
            </style>
        </head>
        <body>
            <div class='container'>
                <h1>Nouvelle demande de prêt</h1>
                
                <h2>Informations sur le prêt</h2>
                <div class='info'>
                    <p><span class='label'>Type de prêt:</span> {$data['loan_type']}</p>
                    <p><span class='label'>Montant souhaité:</span> {$data['amount']} €</p>
                    <p><span class='label'>Durée souhaitée:</span> {$data['duration']} mois</p>
                </div>
                
                <h2>Informations personnelles</h2>
                <div class='info'>
                    <p><span class='label'>Nom:</span> {$data['last_name']}</p>
                    <p><span class='label'>Prénom:</span> {$data['first_name']}</p>
                    <p><span class='label'>Email:</span> {$data['email']}</p>
                    <p><span class='label'>Téléphone:</span> {$data['phone']}</p>
                    <p><span class='label'>Adresse:</span> {$data['address']}</p>
                    <p><span class='label'>Ville:</span> {$data['city']}</p>
                    <p><span class='label'>Code postal:</span> {$data['zip_code']}</p>
                </div>
                
                <h2>Informations financières</h2>
                <div class='info'>
                    <p><span class='label'>Situation professionnelle:</span> {$data['employment_status']}</p>
                    <p><span class='label'>Revenu mensuel net:</span> {$data['monthly_income']} €</p>
                </div>
                
                " . (isset($data['comments']) && !empty($data['comments']) ? "
                <h2>Commentaires supplémentaires</h2>
                <div class='info'>
                    <p>" . nl2br(htmlspecialchars($data['comments'])) . "</p>
                </div>
                " : "") . "
            </div>
        </body>
        </html>
        ";
        
        $mailer->setBody($message);
        
        return $mailer;
    }
}
