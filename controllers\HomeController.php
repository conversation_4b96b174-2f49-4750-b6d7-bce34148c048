<?php
/**
 * Contrôleur de la page d'accueil
 */
require_once ROOT_DIR . '/controllers/BaseController.php';

class HomeController extends BaseController {
    /**
     * Affiche la page d'accueil
     * 
     * @param array $params Paramètres de la requête
     */
    public function render($params = []) {
        // Préparer les données pour la vue
        $data = [
            'title' => $this->contentManager->get('global.siteName') . ' - ' . $this->contentManager->get('global.navigation.home'),
            'global' => $this->contentManager->get('global'),
            'content' => $this->contentManager->get('home'),
            'loans' => $this->contentManager->get('loans'),
            'currentLang' => $this->languageManager->getCurrentLanguage(),
            'availableLangs' => $this->languageManager->getAvailableLanguages()
        ];
        
        // Charger la vue
        $this->loadView('home', $data);
    }
}
