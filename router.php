<?php
/**
 * Routeur
 * Gère le routage des requêtes vers les contrôleurs appropriés
 */
class Router {
    private $languageManager;
    private $contentManager;
    private $routes = [
        'home' => 'HomeController',
        'about' => 'AboutController',
        'contact' => 'ContactController',
        'loans' => 'LoansController',
        'apply' => 'ApplyController',
        'thank-you' => 'ThankYouController'
    ];

    /**
     * Constructeur
     *
     * @param LanguageManager $languageManager Gestionnaire de langue
     * @param ContentManager $contentManager Gestionnaire de contenu
     */
    public function __construct($languageManager, $contentManager) {
        $this->languageManager = $languageManager;
        $this->contentManager = $contentManager;
    }

    /**
     * Traite la requête entrante
     */
    public function processRequest() {
        $uri = $_SERVER['REQUEST_URI'];
        $path = parse_url($uri, PHP_URL_PATH);
        $segments = explode('/', trim($path, '/'));

        // Vérifier si le premier segment est une langue
        $lang = $this->languageManager->getCurrentLanguage();
        $pageIndex = 0;

        if (!empty($segments[0]) && in_array($segments[0], AVAILABLE_LANGS)) {
            $pageIndex = 1;
        }

        // Déterminer la page demandée
        $page = !empty($segments[$pageIndex]) ? $segments[$pageIndex] : 'home';

        // Paramètres supplémentaires
        $params = array_slice($segments, $pageIndex + 1);

        // Charger le contrôleur approprié
        $this->loadController($page, $params);
    }

    /**
     * Charge le contrôleur approprié
     *
     * @param string $page Nom de la page
     * @param array $params Paramètres supplémentaires
     */
    private function loadController($page, $params = []) {
        // Vérifier si la page existe dans les routes
        if (isset($this->routes[$page])) {
            $controllerName = $this->routes[$page];
            $controllerFile = ROOT_DIR . '/controllers/' . $controllerName . '.php';

            if (file_exists($controllerFile)) {
                require_once $controllerFile;
                $controller = new $controllerName($this->languageManager, $this->contentManager);
                $controller->render($params);
                return;
            }
        }

        // Page non trouvée, charger la page d'erreur 404
        $this->loadErrorPage(404);
    }

    /**
     * Charge une page d'erreur
     *
     * @param int $errorCode Code d'erreur HTTP
     */
    private function loadErrorPage($errorCode) {
        http_response_code($errorCode);

        $errorFile = ROOT_DIR . '/views/errors/' . $errorCode . '.php';

        $global = $this->contentManager->get('global');

        if (file_exists($errorFile)) {
            include $errorFile;
        } else {
            // Page d'erreur par défaut
            echo '<h1>Erreur ' . $errorCode . '</h1>';
            echo '<p>La page demandée n\'a pas été trouvée.</p>';
            echo '<p><a href="/">Retour à l\'accueil</a></p>';
        }
    }
}
