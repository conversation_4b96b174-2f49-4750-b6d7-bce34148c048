/*
 * Custom CSS for FinancExpert Loan Website
 * Author: AI Assistant
 * Version: 1.0
 */

/* Global Styles */
:root {
    /* Light mode (default) */
    --primary-color: #0056b3;
    --secondary-color: #6c757d;
    --accent-color: #ffc107;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --font-family-sans-serif: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-serif: 'Georgia', 'Times New Roman', serif;

    /* Base colors */
    --body-bg: #ffffff;
    --body-color: #333333;
    --card-bg: #ffffff;
    --card-border: rgba(0, 0, 0, 0.125);
    --navbar-bg: #ffffff;
    --navbar-color: #333333;
    --footer-bg: #343a40;
    --footer-color: #ffffff;
    --input-bg: #ffffff;
    --input-color: #333333;
    --input-border: #ced4da;
    --dropdown-bg: #ffffff;
    --dropdown-color: #333333;
    --dropdown-hover-bg: #f8f9fa;
    --dropdown-hover-color: #16181b;
    --shadow-color: rgba(0, 0, 0, 0.15);
}

/* Dark mode */
[data-theme="dark"] {
    --primary-color: #3d8bfd;
    --secondary-color: #8c959f;
    --accent-color: #ffca2c;
    --light-color: #212529;
    --dark-color: #f8f9fa;
    --success-color: #42b883;
    --info-color: #4cc9f0;
    --warning-color: #ffca2c;
    --danger-color: #ff6b6b;

    /* Base colors */
    --body-bg: #121212;
    --body-color: #e0e0e0;
    --card-bg: #1e1e1e;
    --card-border: rgba(255, 255, 255, 0.125);
    --navbar-bg: #1e1e1e;
    --navbar-color: #e0e0e0;
    --footer-bg: #1e1e1e;
    --footer-color: #e0e0e0;
    --input-bg: #2d2d2d;
    --input-color: #e0e0e0;
    --input-border: #444444;
    --dropdown-bg: #2d2d2d;
    --dropdown-color: #e0e0e0;
    --dropdown-hover-bg: #3d3d3d;
    --dropdown-hover-color: #ffffff;
    --shadow-color: rgba(0, 0, 0, 0.5);
    --muted-color: rgba(255, 255, 255, 0.6);
    --link-color: #5c9eff;
    --link-hover-color: #80b5ff;
}

body {
    font-family: var(--font-family-sans-serif);
    color: var(--body-color);
    background-color: var(--body-bg);
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    margin-bottom: 1rem;
}

.section-title {
    position: relative;
    margin-bottom: 2rem;
    font-weight: 700;
}

.section-title:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
    margin: 15px auto 0;
}

/* Header Styles */
.top-bar {
    font-size: 0.9rem;
}

.navbar {
    padding: 15px 0;
    background-color: var(--navbar-bg) !important;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.navbar-light .navbar-nav .nav-link {
    color: var(--navbar-color);
}

.navbar-light .navbar-toggler {
    color: var(--navbar-color);
    border-color: var(--navbar-color);
}

.navbar-light .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

[data-theme="dark"] .navbar-light .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar-brand img {
    max-height: 40px;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem !important;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem var(--shadow-color);
    background-color: var(--dropdown-bg);
    transition: background-color 0.3s ease;
}

.dropdown-item {
    color: var(--dropdown-color);
}

.dropdown-item:hover, .dropdown-item:focus {
    color: var(--dropdown-hover-color);
    background-color: var(--dropdown-hover-bg);
}

/* Hero Section */
.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-image {
    background-image: url('/assets/images/hero-bg.jpg'); /* Fallback */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    padding: 80px 0;
}

@supports (background-image: url("image.avif")) {
    .hero-image {
        background-image: url('/assets/images/hero-bg.avif');
    }
}

.hero-image .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1;
}

.hero-image .container {
    position: relative;
    z-index: 2;
}

.min-vh-50 {
    min-height: 30vh;
}

.hero-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

/* Card Styles */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
    border-radius: 0.5rem;
    overflow: hidden;
    background-color: var(--card-bg);
    border-color: var(--card-border);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 3rem var(--shadow-color) !important;
}

.card-body {
    color: var(--body-color);
}

.card-img-top {
    height: 225px;
    object-fit: cover;
}

.card-img-team {
    height: 350px;
    object-fit: cover;
}

.icon-box {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0, 86, 179, 0.1);
}

/* Testimonials */
.testimonial-img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid var(--primary-color);
}

/* Accordion */
.accordion-button:not(.collapsed) {
    background-color: rgba(0, 86, 179, 0.1);
    color: var(--primary-color);
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: rgba(0, 86, 179, 0.25);
}

/* Footer */
footer {
    background-color: var(--footer-bg);
    color: var(--footer-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: background-color 0.3s ease;
}

.social-links a:hover {
    background-color: var(--primary-color);
}

/* Form Elements */
.form-control, .form-select {
    background-color: var(--input-bg);
    color: var(--input-color);
    border-color: var(--input-border);
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.form-control:focus, .form-select:focus {
    background-color: var(--input-bg);
    color: var(--input-color);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb, 0, 86, 179), 0.25);
}

/* Buttons */
.btn {
    padding: 0.5rem 1.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #004494;
    border-color: #004494;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Responsive Adjustments */
@media (max-width: 991.98px) {
    .navbar-nav {
        margin-top: 15px;
    }

    .nav-item {
        margin-bottom: 5px;
    }

    .hero-image {
        padding: 80px 0;
    }
}

@media (max-width: 767.98px) {
    .hero-image {
        padding: 60px 0;
    }

    .display-4 {
        font-size: 2.5rem;
    }

    .lead {
        font-size: 1rem;
    }
}

/* Theme Toggle */
#theme-toggle {
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    padding: 0;
    border-radius: 50%;
}

#theme-toggle svg {
    width: 18px;
    height: 18px;
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 1s ease forwards;
}

/* Dark mode specific overrides */
[data-theme="dark"] .bg-light {
    background-color: var(--card-bg) !important;
}

[data-theme="dark"] .bg-white {
    background-color: var(--navbar-bg) !important;
}

[data-theme="dark"] .bg-dark {
    background-color: var(--footer-bg) !important;
}

[data-theme="dark"] .text-dark {
    color: var(--body-color) !important;
}

[data-theme="dark"] .text-muted {
    color: var(--muted-color) !important;
}

[data-theme="dark"] a:not(.btn) {
    color: var(--link-color);
}

[data-theme="dark"] a:not(.btn):hover {
    color: var(--link-hover-color);
}

[data-theme="dark"] .text-secondary {
    color: #a1a8ae !important;
}

[data-theme="dark"] .border {
    border-color: var(--card-border) !important;
}

[data-theme="dark"] .shadow-sm {
    box-shadow: 0 .125rem .25rem var(--shadow-color) !important;
}

[data-theme="dark"] .shadow {
    box-shadow: 0 .5rem 1rem var(--shadow-color) !important;
}

[data-theme="dark"] .accordion-button {
    background-color: var(--card-bg);
    color: var(--body-color);
    border-color: var(--card-border);
}

[data-theme="dark"] .accordion-button:not(.collapsed) {
    background-color: rgba(61, 139, 253, 0.15);
    color: var(--primary-color);
}

[data-theme="dark"] .accordion-button::after {
    filter: invert(1);
}

[data-theme="dark"] .accordion-item {
    background-color: var(--card-bg);
    border-color: var(--card-border);
}

[data-theme="dark"] .accordion-body {
    background-color: var(--card-bg);
    color: var(--body-color);
}

[data-theme="dark"] .accordion-body p,
[data-theme="dark"] .accordion-body li,
[data-theme="dark"] .accordion-body span {
    color: var(--body-color);
}

[data-theme="dark"] .list-group-item {
    background-color: var(--card-bg);
    color: var(--body-color);
    border-color: var(--card-border);
}

/* Table styles for dark mode */
[data-theme="dark"] .table {
    color: var(--body-color) !important;
    background-color: var(--card-bg) !important;
}

[data-theme="dark"] .table-bordered,
[data-theme="dark"] .table-bordered th,
[data-theme="dark"] .table-bordered td {
    border-color: var(--card-border) !important;
}

[data-theme="dark"] .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05) !important;
}

[data-theme="dark"] .table-striped tbody tr:nth-of-type(even) {
    background-color: var(--card-bg) !important;
}

[data-theme="dark"] .table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.075) !important;
}

[data-theme="dark"] .table thead th {
    border-bottom-color: var(--card-border) !important;
    background-color: rgba(255, 255, 255, 0.05) !important;
    color: var(--body-color) !important;
}

/* Bootstrap table variants in dark mode */
[data-theme="dark"] .table-primary,
[data-theme="dark"] .table-primary > th,
[data-theme="dark"] .table-primary > td {
    background-color: rgba(61, 139, 253, 0.2) !important;
    color: var(--body-color) !important;
}

[data-theme="dark"] .table-secondary,
[data-theme="dark"] .table-secondary > th,
[data-theme="dark"] .table-secondary > td {
    background-color: rgba(140, 149, 159, 0.2) !important;
    color: var(--body-color) !important;
}

[data-theme="dark"] .table-success,
[data-theme="dark"] .table-success > th,
[data-theme="dark"] .table-success > td {
    background-color: rgba(66, 184, 131, 0.2) !important;
    color: var(--body-color) !important;
}

[data-theme="dark"] .table-info,
[data-theme="dark"] .table-info > th,
[data-theme="dark"] .table-info > td {
    background-color: rgba(76, 201, 240, 0.2) !important;
    color: var(--body-color) !important;
}

[data-theme="dark"] .table-warning,
[data-theme="dark"] .table-warning > th,
[data-theme="dark"] .table-warning > td {
    background-color: rgba(255, 202, 44, 0.2) !important;
    color: var(--body-color) !important;
}

[data-theme="dark"] .table-danger,
[data-theme="dark"] .table-danger > th,
[data-theme="dark"] .table-danger > td {
    background-color: rgba(255, 107, 107, 0.2) !important;
    color: var(--body-color) !important;
}

[data-theme="dark"] .table-light,
[data-theme="dark"] .table-light > th,
[data-theme="dark"] .table-light > td {
    background-color: rgba(248, 249, 250, 0.1) !important;
    color: var(--body-color) !important;
}

[data-theme="dark"] .table-dark,
[data-theme="dark"] .table-dark > th,
[data-theme="dark"] .table-dark > td {
    background-color: rgba(52, 58, 64, 0.8) !important;
    color: var(--body-color) !important;
}

/* Fix for white background in tables */
[data-theme="dark"] .table,
[data-theme="dark"] .table tbody,
[data-theme="dark"] .table tbody tr,
[data-theme="dark"] .table tbody td,
[data-theme="dark"] .table thead,
[data-theme="dark"] .table thead tr,
[data-theme="dark"] .table thead th,
[data-theme="dark"] .table tfoot,
[data-theme="dark"] .table tfoot tr,
[data-theme="dark"] .table tfoot td {
    background-color: var(--card-bg) !important;
    color: var(--body-color) !important;
}

/* Force background color for table headers with high specificity */
[data-theme="dark"] th,
[data-theme="dark"] thead th,
[data-theme="dark"] tbody th,
[data-theme="dark"] tfoot th,
[data-theme="dark"] .table th,
[data-theme="dark"] .table thead th,
[data-theme="dark"] .table tbody th,
[data-theme="dark"] .table tfoot th,
[data-theme="dark"] table > thead > tr > th,
[data-theme="dark"] table > tbody > tr > th,
[data-theme="dark"] table > tfoot > tr > th,
[data-theme="dark"] .table > thead > tr > th,
[data-theme="dark"] .table > tbody > tr > th,
[data-theme="dark"] .table > tfoot > tr > th {
    background-color: var(--card-bg) !important;
    color: var(--body-color) !important;
    border-color: var(--card-border) !important;
}

/* Force text color for all table elements */
[data-theme="dark"] table,
[data-theme="dark"] tr,
[data-theme="dark"] td,
[data-theme="dark"] th,
[data-theme="dark"] thead,
[data-theme="dark"] tbody,
[data-theme="dark"] tfoot,
[data-theme="dark"] caption {
    color: var(--body-color) !important;
}

/* Additional specificity for Bootstrap tables */
[data-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > * {
    background-color: rgba(255, 255, 255, 0.05) !important;
    color: var(--body-color) !important;
}

[data-theme="dark"] .table-hover > tbody > tr:hover > * {
    background-color: rgba(255, 255, 255, 0.075) !important;
    color: var(--body-color) !important;
}

/* Table caption */
[data-theme="dark"] .table caption {
    color: var(--muted-color) !important;
    background-color: var(--card-bg) !important;
}

/* Responsive tables */
[data-theme="dark"] .table-responsive {
    background-color: var(--card-bg) !important;
}

/* Table head and foot */
[data-theme="dark"] .table > :not(caption) > * > * {
    color: var(--body-color) !important;
    background-color: var(--card-bg) !important;
}

/* Override Bootstrap's default table styles */
[data-theme="dark"] .table-striped>tbody>tr:nth-of-type(odd)>* {
    --bs-table-accent-bg: rgba(255, 255, 255, 0.05) !important;
}

[data-theme="dark"] .table-hover>tbody>tr:hover>* {
    --bs-table-accent-bg: rgba(255, 255, 255, 0.075) !important;
}

/* Button styles for dark mode */
[data-theme="dark"] .btn {
    filter: none !important;
    text-shadow: none !important;
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
    font-weight: 500 !important;
    letter-spacing: 0.01em !important;
}

/* Fix for <a> buttons */
[data-theme="dark"] a.btn {
    text-decoration: none !important;
    filter: none !important;
    text-shadow: none !important;
    -webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
}

[data-theme="dark"] .btn-primary {
    background-color: #1a73e8 !important;
    border-color: #1a73e8 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .btn-primary:hover,
[data-theme="dark"] .btn-primary:focus,
[data-theme="dark"] .btn-primary:active {
    background-color: #1765cc !important;
    border-color: #1765cc !important;
    color: #ffffff !important;
}

[data-theme="dark"] .btn-secondary {
    background-color: #5f6368 !important;
    border-color: #5f6368 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .btn-secondary:hover,
[data-theme="dark"] .btn-secondary:focus,
[data-theme="dark"] .btn-secondary:active {
    background-color: #4e5256 !important;
    border-color: #4e5256 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .btn-success {
    background-color: #34a853 !important;
    border-color: #34a853 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .btn-success:hover,
[data-theme="dark"] .btn-success:focus,
[data-theme="dark"] .btn-success:active {
    background-color: #2d9249 !important;
    border-color: #2d9249 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .btn-danger {
    background-color: #ea4335 !important;
    border-color: #ea4335 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .btn-danger:hover,
[data-theme="dark"] .btn-danger:focus,
[data-theme="dark"] .btn-danger:active {
    background-color: #d33426 !important;
    border-color: #d33426 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .btn-warning {
    background-color: #fbbc05 !important;
    border-color: #fbbc05 !important;
    color: #000000 !important;
}

[data-theme="dark"] .btn-warning:hover,
[data-theme="dark"] .btn-warning:focus,
[data-theme="dark"] .btn-warning:active {
    background-color: #e8ac04 !important;
    border-color: #e8ac04 !important;
    color: #000000 !important;
}

[data-theme="dark"] .btn-info {
    background-color: #4cc9f0 !important;
    border-color: #4cc9f0 !important;
    color: #000000 !important;
}

[data-theme="dark"] .btn-info:hover,
[data-theme="dark"] .btn-info:focus,
[data-theme="dark"] .btn-info:active {
    background-color: #33b9e2 !important;
    border-color: #33b9e2 !important;
    color: #000000 !important;
}

[data-theme="dark"] .btn-light {
    background-color: #3c4043 !important;
    border-color: #3c4043 !important;
    color: #e8eaed !important;
}

[data-theme="dark"] .btn-light:hover,
[data-theme="dark"] .btn-light:focus,
[data-theme="dark"] .btn-light:active {
    background-color: #4e5256 !important;
    border-color: #4e5256 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .btn-outline-primary {
    color: #4285f4 !important;
    border-color: #4285f4 !important;
}

[data-theme="dark"] .btn-outline-primary:hover,
[data-theme="dark"] .btn-outline-primary:focus,
[data-theme="dark"] .btn-outline-primary:active {
    background-color: #4285f4 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .btn-outline-light {
    color: #e8eaed !important;
    border-color: #5f6368 !important;
}

[data-theme="dark"] .btn-outline-light:hover,
[data-theme="dark"] .btn-outline-light:focus,
[data-theme="dark"] .btn-outline-light:active {
    background-color: #5f6368 !important;
    color: #ffffff !important;
}

/* Additional dark mode styles for forms and other elements */
[data-theme="dark"] .form-control::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

[data-theme="dark"] .form-text {
    color: rgba(255, 255, 255, 0.6);
}

[data-theme="dark"] .form-control:disabled,
[data-theme="dark"] .form-control[readonly] {
    background-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.5);
}

[data-theme="dark"] .badge {
    border: 1px solid var(--card-border);
}

[data-theme="dark"] .badge-light {
    background-color: var(--card-bg);
    color: var(--body-color);
}

[data-theme="dark"] .modal-content {
    background-color: var(--card-bg);
    color: var(--body-color);
    border-color: var(--card-border);
}

[data-theme="dark"] .modal-header,
[data-theme="dark"] .modal-footer {
    border-color: var(--card-border);
}

[data-theme="dark"] .close {
    color: var(--body-color);
}

[data-theme="dark"] .pagination .page-link {
    background-color: var(--card-bg);
    border-color: var(--card-border);
    color: var(--body-color);
}

[data-theme="dark"] .pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
}

[data-theme="dark"] .pagination .page-item.disabled .page-link {
    background-color: var(--card-bg);
    color: rgba(255, 255, 255, 0.4);
}

[data-theme="dark"] .breadcrumb {
    background-color: var(--card-bg);
}

[data-theme="dark"] .breadcrumb-item.active {
    color: rgba(255, 255, 255, 0.6);
}

[data-theme="dark"] .breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.4);
}

[data-theme="dark"] .card-header,
[data-theme="dark"] .card-footer {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: var(--card-border);
}

/* Smooth transition for all elements */
*, *::before, *::after {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}
