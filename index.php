<?php
/**
 * Point d'entrée principal de l'application
 * Gère le routage et l'initialisation
 */

// Inclusion du fichier de configuration
require_once 'config.php';

// Inclusion des fichiers nécessaires
require_once 'router.php';
require_once 'includes/LanguageManager.php';
require_once 'includes/ContentManager.php';

// Initialisation du gestionnaire de langue
$languageManager = new LanguageManager();
$lang = $languageManager->getCurrentLanguage();

// Initialisation du gestionnaire de contenu
$contentManager = new ContentManager($lang);

// Initialisation du routeur
$router = new Router($languageManager, $contentManager);

// Traitement de la requête
$router->processRequest();
