<?php
/**
 * Gestionnaire de langue
 * Gère la détection et le changement de langue
 */
class LanguageManager {
    private $currentLang;
    private $availableLangs;
    
    /**
     * Constructeur
     */
    public function __construct() {
        $this->availableLangs = AVAILABLE_LANGS;
        $this->detectLanguage();
    }
    
    /**
     * Détecte la langue à utiliser
     * Priorité : 1. URL, 2. Session, 3. Navigateur, 4. Défaut
     */
    private function detectLanguage() {
        // 1. Vérifier l'URL
        $urlLang = $this->getLanguageFromUrl();
        if ($urlLang) {
            $this->setCurrentLanguage($urlLang);
            return;
        }
        
        // 2. Vérifier la session
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if (isset($_SESSION['lang']) && in_array($_SESSION['lang'], $this->availableLangs)) {
            $this->setCurrentLanguage($_SESSION['lang']);
            return;
        }
        
        // 3. Vérifier les préférences du navigateur
        $browserLang = $this->getBrowserLanguage();
        if ($browserLang) {
            $this->setCurrentLanguage($browserLang);
            return;
        }
        
        // 4. Utiliser la langue par défaut
        $this->setCurrentLanguage(DEFAULT_LANG);
    }
    
    /**
     * Récupère la langue depuis l'URL
     * Format attendu : /fr/page, /en/page, etc.
     * 
     * @return string|null Code de langue ou null si non trouvé
     */
    private function getLanguageFromUrl() {
        $uri = $_SERVER['REQUEST_URI'];
        $path = parse_url($uri, PHP_URL_PATH);
        $segments = explode('/', trim($path, '/'));
        
        if (!empty($segments[0]) && in_array($segments[0], $this->availableLangs)) {
            return $segments[0];
        }
        
        return null;
    }
    
    /**
     * Récupère la langue préférée du navigateur
     * 
     * @return string|null Code de langue ou null si non supportée
     */
    private function getBrowserLanguage() {
        if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
            $browserLangs = explode(',', $_SERVER['HTTP_ACCEPT_LANGUAGE']);
            
            foreach ($browserLangs as $browserLang) {
                $langCode = substr($browserLang, 0, 2);
                
                if (in_array($langCode, $this->availableLangs)) {
                    return $langCode;
                }
            }
        }
        
        return null;
    }
    
    /**
     * Définit la langue courante
     * 
     * @param string $lang Code de langue
     */
    public function setCurrentLanguage($lang) {
        if (in_array($lang, $this->availableLangs)) {
            $this->currentLang = $lang;
            
            // Stocker dans la session
            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }
            $_SESSION['lang'] = $lang;
        }
    }
    
    /**
     * Récupère la langue courante
     * 
     * @return string Code de langue
     */
    public function getCurrentLanguage() {
        return $this->currentLang;
    }
    
    /**
     * Récupère les langues disponibles
     * 
     * @return array Liste des langues disponibles
     */
    public function getAvailableLanguages() {
        return $this->availableLangs;
    }
    
    /**
     * Génère une URL avec la langue spécifiée
     * 
     * @param string $path Chemin de l'URL
     * @param string $lang Code de langue (optionnel, utilise la langue courante par défaut)
     * @return string URL avec la langue
     */
    public function getLocalizedUrl($path = '', $lang = null) {
        $lang = $lang ?: $this->currentLang;
        
        // Nettoyer le chemin des éventuels codes de langue
        $cleanPath = $path;
        foreach ($this->availableLangs as $availableLang) {
            $cleanPath = preg_replace('#^/' . $availableLang . '(/)?#', '/', $cleanPath);
        }
        
        // Construire l'URL localisée
        return '/' . $lang . ($cleanPath != '/' ? $cleanPath : '');
    }
}
