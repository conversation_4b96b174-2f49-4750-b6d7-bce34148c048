<!-- Hero Section -->
<section class="page-header bg-primary text-white">
    <div class="container">
        <div class="row py-5">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3"><?php echo $about['title']; ?></h1>
                <p class="lead mb-0"><?php echo $about['subtitle']; ?></p>
            </div>
        </div>
    </div>
</section>

<!-- About Intro Section -->
<section class="py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <picture>
                    <source srcset="/assets/images/about-intro.avif" type="image/avif">
                    <source srcset="/assets/images/about-intro.webp" type="image/webp">
                    <img src="/assets/images/about-intro.jpg" class="img-fluid rounded shadow-sm" alt="<?php echo $about['title']; ?>" width="600" height="450">
                </picture>
            </div>
            <div class="col-lg-6">
                <h2 class="mb-4"><?php echo $about['intro']['title']; ?></h2>
                <p class="lead mb-4"><?php echo $about['intro']['subtitle']; ?></p>
                <p class="mb-4"><?php echo $about['intro']['content']; ?></p>
                <p><?php echo $about['intro']['content2']; ?></p>
            </div>
        </div>
    </div>
</section>

<!-- Mission & Values Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-4"><?php echo $about['mission']['title']; ?></h2>
                <p class="lead"><?php echo $about['mission']['subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row mb-5">
            <div class="col-lg-6 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h3 class="card-title mb-4"><?php echo $about['mission']['missionTitle']; ?></h3>
                        <p class="card-text"><?php echo $about['mission']['missionContent']; ?></p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h3 class="card-title mb-4"><?php echo $about['mission']['visionTitle']; ?></h3>
                        <p class="card-text"><?php echo $about['mission']['visionContent']; ?></p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($about['values']['items'] as $value): ?>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="icon-box mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="60" height="48" fill="#007bff" viewBox="0 0 512 512">
                                    <?php echo $value['icon']; ?>
                                </svg>
                            </div>
                            <h4 class="card-title mb-3"><?php echo $value['title']; ?></h4>
                            <p class="card-text"><?php echo $value['description']; ?></p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Team Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-4"><?php echo $about['team']['title']; ?></h2>
                <p class="lead"><?php echo $about['team']['subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($about['team']['members'] as $member): ?>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <picture>
                            <source srcset="/assets/images/<?php echo $member['image']; ?>.avif" type="image/avif">
                            <source srcset="/assets/images/<?php echo $member['image']; ?>.webp" type="image/webp">
                            <img src="/assets/images/<?php echo $member['image']; ?>.jpg" class="card-img-team" alt="<?php echo $member['name']; ?>" width="310" loading="lazy">
                        </picture>
                        <div class="card-body text-center p-4">
                            <h4 class="card-title mb-1"><?php echo $member['name']; ?></h4>
                            <p class="text-muted mb-3"><?php echo $member['position']; ?></p>
                            <p class="card-text"><?php echo $member['bio']; ?></p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-4"><?php echo $about['stats']['title']; ?></h2>
                <p class="lead"><?php echo $about['stats']['subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($about['stats']['items'] as $stat): ?>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="icon-box mb-4">
                                <svg class="text-primary" width="60" height="48" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">
                                    <?php echo $stat['icon']; ?>
                                </svg>
                            </div>
                            <h3 class="display-5 fw-bold mb-2"><?php echo $stat['value']; ?></h3>
                            <p class="text-muted mb-0"><?php echo $stat['label']; ?></p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-4"><?php echo $content['testimonials']['title']; ?></h2>
                <p class="lead"><?php echo $content['testimonials']['subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($content['testimonials']['items'] as $testimonial): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center mb-4">
                                <picture class="me-3">
                                    <source srcset="/assets/images/<?php echo $testimonial['image']; ?>.avif" type="image/avif">
                                    <source srcset="/assets/images/<?php echo $testimonial['image']; ?>.webp" type="image/webp">
                                    <img src="/assets/images/<?php echo $testimonial['image']; ?>.jpg" class="rounded-circle" alt="<?php echo $testimonial['author']; ?>" width="60" height="60">
                                </picture>
                                <div>
                                    <h5 class="mb-1"><?php echo $testimonial['author']; ?></h5>
                                    <p class="text-muted mb-0"><?php echo $testimonial['position']; ?></p>
                                </div>
                            </div>
                            <div class="mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="text-primary opacity-25" viewBox="0 0 512 512">
                                    <path d="M0 216C0 149.7 53.7 96 120 96l8 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-8 0c-30.9 0-56 25.1-56 56l0 8 64 0c35.3 0 64 28.7 64 64l0 64c0 35.3-28.7 64-64 64l-64 0c-35.3 0-64-28.7-64-64l0-32 0-32 0-72zm256 0c0-66.3 53.7-120 120-120l8 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-8 0c-30.9 0-56 25.1-56 56l0 8 64 0c35.3 0 64 28.7 64 64l0 64c0 35.3-28.7 64-64 64l-64 0c-35.3 0-64-28.7-64-64l0-32 0-32 0-72z"/>
                                </svg>
                            </div>
                            <p class="card-text"><?php echo $testimonial['quote']; ?></p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Partners Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-4"><?php echo $about['partners']['title']; ?></h2>
                <p class="lead"><?php echo $about['partners']['subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="row align-items-center text-center">
                    <?php foreach ($about['partners']['items'] as $partner): ?>
                        <div class="col-md-4 col-6 mb-4">
                            <picture>
                                <source srcset="/assets/images/<?php echo $partner['logo']; ?>.avif" type="image/avif">
                                <source srcset="/assets/images/<?php echo $partner['logo']; ?>.webp" type="image/webp">
                                <img src="/assets/images/<?php echo $partner['logo']; ?>.png" class="img-fluid" alt="<?php echo $partner['name']; ?>" width="270" style="max-height: 80px;" loading="lazy">
                            </picture>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h2 class="mb-3"><?php echo $content['cta']['title']; ?></h2>
                <p class="lead mb-4"><?php echo $content['cta']['subtitle']; ?></p>
                <a href="<?php echo $this->languageManager->getLocalizedUrl('/contact'); ?>" class="btn btn-light btn-lg mb-3">
                    <?php echo $about['cta']['button']; ?>
                </a>
                <p class="mb-0"><?php echo $content['cta']['contact']; ?></p>
            </div>
        </div>
    </div>
</section>
