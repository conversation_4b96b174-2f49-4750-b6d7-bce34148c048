<!-- Thank You Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <div class="mb-4 text-success">
                    <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" fill="currentColor" class="text-success" viewBox="0 0 512 512">
                        <path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"/>
                    </svg>
                </div>
                <h1 class="display-4 fw-bold mb-4"><?php echo $thankYou['title']; ?></h1>
                <p class="lead mb-4"><?php echo $thankYou['message']; ?></p>
                <p class="mb-5"><?php echo $thankYou['followUp']; ?></p>
                
                <div class="d-flex justify-content-center gap-3">
                    <a href="<?php echo $this->languageManager->getLocalizedUrl('/'); ?>" class="btn btn-outline-primary btn-lg">
                        <?php echo $thankYou['homeButton']; ?>
                    </a>
                    <?php if (isset($thankYou['secondaryAction'])): ?>
                        <a href="<?php echo $this->languageManager->getLocalizedUrl($thankYou['secondaryAction']['url']); ?>" class="btn btn-primary btn-lg">
                            <?php echo $thankYou['secondaryAction']['text']; ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Content Section -->
<?php if (isset($thankYou['relatedContent'])): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-3"><?php echo $thankYou['relatedContent']['title']; ?></h2>
                <p class="lead"><?php echo $thankYou['relatedContent']['subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row">
            <?php 
            $count = 0;
            foreach ($content['loanTypes'] as $relatedLoan):
                if ($count < 3):
                    $count++;
            ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <picture>
                            <source srcset="/assets/images/<?php echo $relatedLoan['image']; ?>.avif" type="image/avif">
                            <source srcset="/assets/images/<?php echo $relatedLoan['image']; ?>.webp" type="image/webp">
                            <img src="/assets/images/<?php echo $relatedLoan['image']; ?>.jpg" class="card-img-top" alt="<?php echo $loan['title']; ?>" loading="lazy">
                        </picture>
                        <div class="card-body p-4">
                            <h4 class="card-title mb-3"><?php echo $relatedLoan['title']; ?></h4>
                            <p class="card-text mb-4"><?php echo $relatedLoan['shortDescription']; ?></p>
                            <a href="<?php echo $this->languageManager->getLocalizedUrl('/loans/' . $relatedLoan['id']); ?>" class="btn btn-outline-primary">
                                <?php echo $relatedLoan['cta']; ?>
                            </a>
                        </div>
                    </div>
                </div>
            <?php 
                endif;
            endforeach; 
            ?>
        </div>
    </div>
</section>
<?php endif; ?>
