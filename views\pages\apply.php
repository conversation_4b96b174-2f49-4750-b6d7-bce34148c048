<!-- Hero Section -->
<section class="page-header bg-primary text-white">
    <div class="container">
        <div class="row py-5">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3"><?php echo $apply['title']; ?></h1>
                <p class="lead mb-0"><?php echo $apply['subtitle']; ?></p>
            </div>
        </div>
    </div>
</section>

<!-- Application Form Section -->
<section class="py-5">
    <div class="container">
        <?php if (!empty($message)): ?>
            <div class="row mb-4">
                <div class="col-lg-10 mx-auto">
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto text-center">
                <p class="lead"><?php echo $apply['intro']; ?></p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4 p-md-5">
                        <form method="post" class="needs-validation" novalidate>
                            <input type="hidden" name="apply_form" value="1">
                            
                            <!-- Loan Information -->
                            <h3 class="mb-4"><?php echo $apply['form']['loanInfoTitle']; ?></h3>
                            
                            <div class="row mb-4">
                                <div class="col-md-6 mb-3 mb-md-0">
                                    <label for="loan_type" class="form-label"><?php echo $apply['form']['loanTypeLabel']; ?> *</label>
                                    <select class="form-select" id="loan_type" name="loan_type" required>
                                        <option value="" selected disabled><?php echo $apply['form']['loanTypePlaceholder']; ?></option>
                                        <?php foreach ($loans['loanTypes'] as $loan): ?>
                                            <option value="<?php echo $loan['id']; ?>" <?php echo ($loanType === $loan['id']) ? 'selected' : ''; ?>>
                                                <?php echo $loan['title']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        <?php echo $apply['form']['loanTypeError']; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="amount" class="form-label"><?php echo $apply['form']['amountLabel']; ?> *</label>
                                    <input type="number" class="form-control" id="amount" name="amount" min="1000" max="1000000" step="500" value="<?php echo $formData['amount'] ?? ''; ?>" required>
                                    <div class="invalid-feedback">
                                        <?php echo $apply['form']['amountError']; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="duration" class="form-label"><?php echo $apply['form']['durationLabel']; ?> *</label>
                                    <input type="number" class="form-control" id="duration" name="duration" min="12" max="360" step="6" value="<?php echo $formData['duration'] ?? ''; ?>" required>
                                    <div class="invalid-feedback">
                                        <?php echo $apply['form']['durationError']; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <hr class="my-5">
                            
                            <!-- Personal Information -->
                            <h3 class="mb-4"><?php echo $apply['form']['personalInfoTitle']; ?></h3>
                            
                            <div class="row mb-4">
                                <div class="col-md-6 mb-3 mb-md-0">
                                    <label for="first_name" class="form-label"><?php echo $apply['form']['firstNameLabel']; ?> *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" value="<?php echo $formData['first_name'] ?? ''; ?>" required>
                                    <div class="invalid-feedback">
                                        <?php echo $apply['form']['firstNameError']; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="last_name" class="form-label"><?php echo $apply['form']['lastNameLabel']; ?> *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" value="<?php echo $formData['last_name'] ?? ''; ?>" required>
                                    <div class="invalid-feedback">
                                        <?php echo $apply['form']['lastNameError']; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mb-4">
                                <div class="col-md-6 mb-3 mb-md-0">
                                    <label for="email" class="form-label"><?php echo $apply['form']['emailLabel']; ?> *</label>
                                    <input type="email" class="form-control" id="email" name="email" value="<?php echo $formData['email'] ?? ''; ?>" required>
                                    <div class="invalid-feedback">
                                        <?php echo $apply['form']['emailError']; ?>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3 mb-md-0">
                                    <label for="confirm_email" class="form-label"><?php echo $apply['form']['confirmEmailLabel']; ?> *</label>
                                    <input type="email" class="form-control" id="confirm_email" name="confirm_email" value="<?php echo $formData['confirm_email'] ?? ''; ?>" required>
                                    <div class="invalid-feedback">
                                        <?php echo $apply['form']['confirmEmailError']; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="phone" class="form-label"><?php echo $apply['form']['phoneLabel']; ?> *</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo $formData['phone'] ?? ''; ?>" required>
                                    <div class="invalid-feedback">
                                        <?php echo $apply['form']['phoneError']; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="address" class="form-label"><?php echo $apply['form']['addressLabel']; ?> *</label>
                                <input type="text" class="form-control" id="address" name="address" value="<?php echo $formData['address'] ?? ''; ?>" required>
                                <div class="invalid-feedback">
                                    <?php echo $apply['form']['addressError']; ?>
                                </div>
                            </div>
                            
                            <div class="row mb-4">
                                <div class="col-md-6 mb-3 mb-md-0">
                                    <label for="city" class="form-label"><?php echo $apply['form']['cityLabel']; ?> *</label>
                                    <input type="text" class="form-control" id="city" name="city" value="<?php echo $formData['city'] ?? ''; ?>" required>
                                    <div class="invalid-feedback">
                                        <?php echo $apply['form']['cityError']; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="country" class="form-label"><?php echo $apply['form']['countryLabel']; ?> *</label>
                                    <select class="form-select" id="country" name="country" required>
                                        <option value="" selected disabled><?php echo $apply['form']['countryPlaceholder']; ?></option>
                                        <?php foreach ($apply['form']['countries'] as $value => $label): ?>
                                            <option value="<?php echo $value; ?>" <?php echo (isset($formData['country']) && $formData['country'] === $value) ? 'selected' : ''; ?>>
                                                <?php echo $label; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        <?php echo $apply['form']['countryError']; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <hr class="my-5">
                            
                            <!-- Financial Information -->
                            <h3 class="mb-4"><?php echo $apply['form']['financialInfoTitle']; ?></h3>
                            
                            <div class="row mb-4">
                                <div class="col-md-6 mb-3 mb-md-0">
                                    <label for="employment_status" class="form-label"><?php echo $apply['form']['employmentStatusLabel']; ?> *</label>
                                    <select class="form-select" id="employment_status" name="employment_status" required>
                                        <option value="" selected disabled><?php echo $apply['form']['employmentStatusPlaceholder']; ?></option>
                                        <?php foreach ($apply['form']['employmentStatuses'] as $value => $label): ?>
                                            <option value="<?php echo $value; ?>" <?php echo (isset($formData['employment_status']) && $formData['employment_status'] === $value) ? 'selected' : ''; ?>>
                                                <?php echo $label; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        <?php echo $apply['form']['employmentStatusError']; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="monthly_income" class="form-label"><?php echo $apply['form']['monthlyIncomeLabel']; ?> *</label>
                                    <input type="number" class="form-control" id="monthly_income" name="monthly_income" min="0" step="100" value="<?php echo $formData['monthly_income'] ?? ''; ?>" required>
                                    <div class="invalid-feedback">
                                        <?php echo $apply['form']['monthlyIncomeError']; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="comments" class="form-label"><?php echo $apply['form']['commentsLabel']; ?></label>
                                <textarea class="form-control" id="comments" name="comments" rows="4"><?php echo $formData['comments'] ?? ''; ?></textarea>
                            </div>
                            
                            <div class="mb-4 form-check">
                                <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms">
                                    <?php echo $apply['form']['termsLabel']; ?> *
                                </label>
                                <div class="invalid-feedback">
                                    <?php echo $apply['form']['termsError']; ?>
                                </div>
                            </div>
                            
                            <div class="text-center mt-5">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <?php echo $apply['form']['submitButton']; ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Process Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-3"><?php echo $content['process']['title']; ?></h2>
                <p class="lead"><?php echo $content['process']['subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($content['process']['steps'] as $step): ?>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="step-number mb-4">
                                <span class="rounded-circle bg-primary text-white d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                    <?php echo $step['number']; ?>
                                </span>
                            </div>
                            <h4 class="card-title mb-3"><?php echo $step['title']; ?></h4>
                            <p class="card-text"><?php echo $step['description']; ?></p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-3"><?php echo $apply['faq']['title'] ?? $content['faq']['title']; ?></h2>
                <p class="lead"><?php echo $apply['faq']['subtitle'] ?? $content['faq']['subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="accordion" id="applyFaqAccordion">
                    <?php 
                    $faqItems = $apply['faq']['items'] ?? $content['faq']['items'];
                    foreach ($faqItems as $index => $faq): 
                    ?>
                        <div class="accordion-item mb-3 border-0 shadow-sm">
                            <h3 class="accordion-header" id="applyHeading<?php echo $index; ?>">
                                <button class="accordion-button <?php echo $index > 0 ? 'collapsed' : ''; ?>" type="button" data-bs-toggle="collapse" data-bs-target="#applyCollapse<?php echo $index; ?>" aria-expanded="<?php echo $index === 0 ? 'true' : 'false'; ?>" aria-controls="applyCollapse<?php echo $index; ?>">
                                    <?php echo $faq['question']; ?>
                                </button>
                            </h3>
                            <div id="applyCollapse<?php echo $index; ?>" class="accordion-collapse collapse <?php echo $index === 0 ? 'show' : ''; ?>" aria-labelledby="applyHeading<?php echo $index; ?>" data-bs-parent="#applyFaqAccordion">
                                <div class="accordion-body">
                                    <?php echo $faq['answer']; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</section>
