<?php
/**
 * Contrôleur de la page de remerciement
 */
require_once ROOT_DIR . '/controllers/BaseController.php';

class ThankYouController extends BaseController {
    /**
     * Affiche la page de remerciement
     * 
     * @param array $params Paramètres de la requête
     */
    public function render($params = []) {
        // Récupérer le type de formulaire depuis l'URL
        $formType = isset($params[0]) ? $params[0] : 'default';
        
        // Préparer les données pour la vue
        $data = [
            'title' => $this->contentManager->get('global.siteName') . ' - ' . $this->contentManager->get('thankYou.title', 'Merci'),
            'thankYou' => $this->contentManager->get('thankYou.' . $formType, $this->contentManager->get('thankYou.default')),
            'global' => $this->contentManager->get('global'),
            'content' => $this->contentManager->get('home'),
            'currentLang' => $this->languageManager->getCurrentLanguage(),
            'availableLangs' => $this->languageManager->getAvailableLanguages()
        ];
        $data['content']['loanTypes'] = $this->contentManager->get('loans.loanTypes');
        
        // Charger la vue
        $this->loadView('thank-you', $data);
    }
}
