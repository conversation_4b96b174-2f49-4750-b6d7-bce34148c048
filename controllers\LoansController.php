<?php
/**
 * Contrôleur pour les pages de prêts
 */
require_once ROOT_DIR . '/controllers/BaseController.php';

class LoansController extends BaseController {
    /**
     * Affiche la page de prêts
     * 
     * @param array $params Paramètres de la requête
     */
    public function render($params = []) {
        // Vérifier si un type de prêt spécifique est demandé
        $loanType = isset($params[0]) ? $params[0] : null;
        
        // Préparer les données pour la vue
        $data = [
            'global' => $this->contentManager->get('global'),
            'currentLang' => $this->languageManager->getCurrentLanguage(),
            'availableLangs' => $this->languageManager->getAvailableLanguages()
        ];
        
        if ($loanType) {
            // Page d'un type de prêt spécifique
            $loanData = $this->contentManager->get('loans.loanTypes.' . $loanType);
            
            if (!$loanData) {
                // Type de prêt non trouvé, rediriger vers la page des prêts
                header('Location: ' . $this->languageManager->getLocalizedUrl('/loans'));
                exit;
            }
            
            $data['title'] = $this->contentManager->get('global.siteName') . ' - ' . $loanData['title'];
            $data['loanDetails'] = $loanData;
            $data['loans'] = $this->contentManager->get('loans');
            $data['loanType'] = $loanType;
            $data['content'] = $this->contentManager->get('home');
            $data['content']['loanTypes'] = $this->contentManager->get('loans.loanTypes');
            
            // Charger la vue du type de prêt spécifique
            $this->loadView('loan-detail', $data);
        } else {
            // Page principale des prêts
            $data['title'] = $this->contentManager->get('global.siteName') . ' - ' . $this->contentManager->get('global.navigation.loans');
            $data['loans'] = $this->contentManager->get('loans');
            $data['content'] = $this->contentManager->get('home');
            $data['content']['loanTypes'] = $this->contentManager->get('loans.loanTypes');
            
            // Charger la vue principale des prêts
            $this->loadView('loans', $data);
        }
    }
}
