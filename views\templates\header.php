<!DOCTYPE html>
<html lang="<?php echo $currentLang; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light dark">
    <meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)">
    <meta name="theme-color" content="#121212" media="(prefers-color-scheme: dark)">
    <title><?php echo $title; ?></title>

    <!-- Bootstrap CSS -->
    <link href="/assets/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="/assets/css/style.css" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" href="/assets/images/favicon.png">
    <link rel="icon" href="/favicon.png">

    <!-- Préchargement Hero background image -->
    <?php if ($view === 'home'): ?>
        <link rel="preload" as="image" href="/assets/images/hero-bg.avif" type="image/avif" fetchpriority="high">
    <?php endif; ?>

</head>
<body>
    <!-- Header -->
    <header>
        <!-- Top Bar -->
        <div class="top-bar bg-dark text-white py-2">
            <div class="container">
                <div class="row align-items-center justify-content-between">
                    <div class="col-auto">
                        <ul class="list-inline mb-0">
                            <li class="list-inline-item">
                                <svg class="me-2" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 512 512">
                                    <path d="M502.3 190.8L327.4 338.5c-23.6 20-59.2 20-82.8 0L9.7 190.8C3.9 186.2 0 179.3 0 171.9V128c0-26.5 21.5-48 48-48h416c26.5 0 48 21.5 48 48v43.9c0 7.4-3.9 14.3-9.7 18.9zM0 205.2V384c0 26.5 21.5 48 48 48h416c26.5 0 48-21.5 48-48V205.2L344.5 344.2c-35.4 30.1-87.6 30.1-123 0L0 205.2z"/>
                                </svg>
                                <?php echo $global['footer']['email']; ?>
                            </li>
                        </ul>
                    </div>
                    <div class="col-auto text-md-end d-flex align-items-center">
                        <!-- Language Selector -->
                        <div class="dropdown d-inline-block me-2">
                            <button class="btn btn-sm btn-dark d-flex align-items-center dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <svg class="me-2" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 512 512">
                                    <path d="M352 256c0 22.2-1.2 43.6-3.3 64l-185.3 0c-2.2-20.4-3.3-41.8-3.3-64s1.2-43.6 3.3-64l185.3 0c2.2 20.4 3.3 41.8 3.3 64zm28.8-64l123.1 0c5.3 20.5 8.1 41.9 8.1 64s-2.8 43.5-8.1 64l-123.1 0c2.1-20.6 3.2-42 3.2-64s-1.1-43.4-3.2-64zm112.6-32l-116.7 0c-10-63.9-29.8-117.4-55.3-151.6c78.3 20.7 142 77.5 171.9 151.6zm-149.1 0l-176.6 0c6.1-36.4 15.5-68.6 27-94.7c10.5-23.6 22.2-40.7 33.5-51.5C239.4 3.2 248.7 0 256 0s16.6 3.2 27.8 13.8c11.3 10.8 23 27.9 33.5 51.5c11.6 26 20.9 58.2 27 94.7zm-209 0L18.6 160C48.6 85.9 112.2 29.1 190.6 8.4C165.1 42.6 145.3 96.1 135.3 160zM8.1 192l123.1 0c-2.1 20.6-3.2 42-3.2 64s1.1 43.4 3.2 64L8.1 320C2.8 299.5 0 278.1 0 256s2.8-43.5 8.1-64zM194.7 446.6c-11.6-26-20.9-58.2-27-94.6l176.6 0c-6.1 36.4-15.5 68.6-27 94.6c-10.5 23.6-22.2 40.7-33.5 51.5C272.6 508.8 263.3 512 256 512s-16.6-3.2-27.8-13.8c-11.3-10.8-23-27.9-33.5-51.5zM135.3 352c10 63.9 29.8 117.4 55.3 151.6C112.2 482.9 48.6 426.1 18.6 352l116.7 0zm358.1 0c-30 74.1-93.6 130.9-171.9 151.6c25.5-34.2 45.2-87.7 55.3-151.6l116.7 0z"/>
                                </svg>
                                <?php echo strtoupper($currentLang); ?>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="languageDropdown">
                                <?php foreach ($availableLangs as $lang): ?>
                                    <?php if ($lang !== $currentLang): ?>
                                        <li>
                                            <a class="dropdown-item d-flex align-items-center" href="<?php echo $this->languageManager->getLocalizedUrl($_SERVER['REQUEST_URI'], $lang); ?>">
                                                <!-- Flag Icon
                                                <picture class="d-flex align-items-center me-2">
                                                    <source type="image/webp" srcset="https://flagcdn.com/w20/<?php echo $lang; ?>.webp">
                                                    <img src="https://flagcdn.com/w20/<?php echo $lang; ?>.png" width="20" alt="">
                                                </picture>
                                                <img class="me-2" src="https://cdnjs.cloudflare.com/ajax/libs/flag-icons/7.3.2/flags/4x3/<?php echo $lang; ?>.svg" width="20" height="15" alt=""> -->
                                                <svg xmlns="http://www.w3.org/2000/svg" id="flag-icons-nl" class="me-2" width="20" height="15" viewBox="0 0 640 480">
                                                    <?php echo $global['flagIcon'][''.$lang.'']; ?>
                                                </svg>
                                                <?php echo strtoupper($lang); ?>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <!-- Theme Toggle -->
                        <button id="theme-toggle" class="btn btn-sm btn-dark d-flex align-items-center" type="button" aria-label="Toggle dark mode">
                            <!-- Sun icon (light mode) -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="theme-icon-light" viewBox="0 0 512 512">
                                <path d="M361.5 1.2c5 2.1 8.6 6.6 9.6 11.9L391 121l107.9 19.8c5.3 1 9.8 4.6 11.9 9.6s1.5 10.7-1.6 15.2L446.9 256l62.3 90.3c3.1 4.5 3.7 10.2 1.6 15.2s-6.6 8.6-11.9 9.6L391 391 371.1 498.9c-1 5.3-4.6 9.8-9.6 11.9s-10.7 1.5-15.2-1.6L256 446.9l-90.3 62.3c-4.5 3.1-10.2 3.7-15.2 1.6s-8.6-6.6-9.6-11.9L121 391 13.1 371.1c-5.3-1-9.8-4.6-11.9-9.6s-1.5-10.7 1.6-15.2L65.1 256 2.8 165.7c-3.1-4.5-3.7-10.2-1.6-15.2s6.6-8.6 11.9-9.6L121 121 140.9 13.1c1-5.3 4.6-9.8 9.6-11.9s10.7-1.5 15.2 1.6L256 65.1 346.3 2.8c4.5-3.1 10.2-3.7 15.2-1.6zM160 256a96 96 0 1 1 192 0 96 96 0 1 1 -192 0zm224 0a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z"/>
                            </svg>
                            <!-- Moon icon (dark mode) -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="theme-icon-dark" viewBox="0 0 384 512" style="display:none;">
                                <path d="M223.5 32C100 32 0 132.3 0 256S100 480 223.5 480c60.6 0 115.5-24.2 155.8-63.4c5-4.9 6.3-12.5 3.1-18.7s-10.1-9.7-17-8.5c-9.8 1.7-19.8 2.6-30.1 2.6c-96.9 0-175.5-78.8-175.5-176c0-65.8 36-123.1 89.3-153.3c6.1-3.5 9.2-10.5 7.7-17.3s-7.3-11.9-14.3-12.5c-6.3-.5-12.6-.8-19-.8z"/>
                            </svg>
                        </button>

                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <a class="navbar-brand" href="<?php echo $this->languageManager->getLocalizedUrl('/'); ?>">
                    <picture>
                        <source srcset="/assets/images/logo.avif" type="image/avif">
                        <source srcset="/assets/images/logo.webp" type="image/webp">
                        <img src="/assets/images/logo.png" alt="<?php echo $global['siteName']; ?>" width="145" height="40" loading="eager" fetchpriority="high">
                    </picture>
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain" aria-controls="navbarMain" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarMain">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $this->languageManager->getLocalizedUrl('/'); ?>">
                                <?php echo $global['navigation']['home']; ?>
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="loansDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <?php echo $global['navigation']['loans']; ?>
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="loansDropdown">
                                <?php foreach ($loans['loanTypes'] as $loan): ?>
                                    <li>
                                        <a class="dropdown-item" href="<?php echo $this->languageManager->getLocalizedUrl('/loans/' . $loan['id']); ?>">
                                            <?php echo $loan['title']; ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $this->languageManager->getLocalizedUrl('/about'); ?>">
                                <?php echo $global['navigation']['about']; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $this->languageManager->getLocalizedUrl('/contact'); ?>">
                                <?php echo $global['navigation']['contact']; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-lg-3" href="<?php echo $this->languageManager->getLocalizedUrl('/apply'); ?>">
                                <?php echo $global['navigation']['apply']; ?>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>