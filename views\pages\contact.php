<!-- Hero Section -->
<section class="page-header bg-primary text-white">
    <div class="container">
        <div class="row py-5">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3"><?php echo $contact['title']; ?></h1>
                <p class="lead mb-0"><?php echo $contact['subtitle']; ?></p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Info Section -->
<section class="py-5">
    <div class="container">
        <?php if (!empty($message)): ?>
            <div class="row mb-4">
                <div class="col-lg-8 mx-auto">
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto text-center">
                <p class="lead"><?php echo $contact['intro']; ?></p>
            </div>
        </div>
        
        <div class="row mb-5">
            <?php foreach ($contact['info'] as $info): ?>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="icon-box mb-4">
                                <svg class="text-primary" width="48" height="48" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">
                                    <?php echo $info['icon']; ?>
                                </svg>
                            </div>
                            <h4 class="card-title mb-3"><?php echo $info['title']; ?></h4>
                            <p class="card-text"><?php echo $info['content']; ?></p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Contact Form Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-4"><?php echo $contact['form']['title']; ?></h2>
                <p class="lead"><?php echo $contact['form']['subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4 p-md-5">
                        <form method="post" class="needs-validation" novalidate>
                            <input type="hidden" name="contact_form" value="1">
                            
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="first_name" class="form-label"><?php echo $contact['form']['firstNameLabel']; ?> *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required>
                                    <div class="invalid-feedback">
                                        <?php echo $contact['form']['firstNameError']; ?>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label for="last_name" class="form-label"><?php echo $contact['form']['lastNameLabel']; ?> *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required>
                                    <div class="invalid-feedback">
                                        <?php echo $contact['form']['lastNameError']; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="email" class="form-label"><?php echo $contact['form']['emailLabel']; ?> *</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                    <div class="invalid-feedback">
                                        <?php echo $contact['form']['emailError']; ?>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label for="confirm_email" class="form-label"><?php echo $contact['form']['confirmEmailLabel']; ?> *</label>
                                    <input type="email" class="form-control" id="confirm_email" name="confirm_email" required>
                                    <div class="invalid-feedback">
                                        <?php echo $contact['form']['confirmEmailError']; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="phone" class="form-label"><?php echo $contact['form']['phoneLabel']; ?></label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                    <div class="invalid-feedback">
                                        <?php echo $contact['form']['phoneError']; ?>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label for="country" class="form-label"><?php echo $contact['form']['countryLabel']; ?> *</label>
                                    <select class="form-select" id="country" name="country" required>
                                        <option value="" selected disabled><?php echo $contact['form']['countryPlaceholder']; ?></option>
                                        <?php foreach ($contact['form']['countries'] as $value => $label): ?>
                                            <option value="<?php echo $value; ?>">
                                                <?php echo $label; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        <?php echo $contact['form']['countryError']; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-12 mb-4">
                                    <label for="subject" class="form-label"><?php echo $contact['form']['subjectLabel']; ?> *</label>
                                    <input type="text" class="form-control" id="subject" name="subject" required>
                                    <div class="invalid-feedback">
                                        <?php echo $contact['form']['subjectError']; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="message" class="form-label"><?php echo $contact['form']['messageLabel']; ?> *</label>
                                <textarea class="form-control" id="message" name="message" rows="5" required></textarea>
                                <div class="invalid-feedback">
                                    <?php echo $contact['form']['messageError']; ?>
                                </div>
                            </div>
                            
                            <div class="mb-4 form-check">
                                <input type="checkbox" class="form-check-input" id="privacy" name="privacy" required>
                                <label class="form-check-label" for="privacy">
                                    <?php echo $contact['form']['privacyLabel']; ?> *
                                </label>
                                <div class="invalid-feedback">
                                    <?php echo $contact['form']['privacyError']; ?>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <?php echo $contact['form']['submitButton']; ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-4"><?php echo $contact['map']['title']; ?></h2>
                <p class="lead"><?php echo $contact['map']['subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-0">
                        <!-- Placeholder for Google Maps (in a real project, you would include the actual Google Maps embed code) -->
                        <div class="ratio ratio-21x9">
                            <iframe src="<?php echo $contact['map']['iframe']; ?>" allowfullscreen="" loading="lazy"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-3"><?php echo $contact['faq']['title']; ?></h2>
                <p class="lead"><?php echo $contact['faq']['subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="accordion" id="contactFaqAccordion">
                    <?php foreach ($contact['faq']['items'] as $index => $faq): ?>
                        <div class="accordion-item mb-3 border-0 shadow-sm">
                            <h3 class="accordion-header" id="contactHeading<?php echo $index; ?>">
                                <button class="accordion-button <?php echo $index > 0 ? 'collapsed' : ''; ?>" type="button" data-bs-toggle="collapse" data-bs-target="#contactCollapse<?php echo $index; ?>" aria-expanded="<?php echo $index === 0 ? 'true' : 'false'; ?>" aria-controls="contactCollapse<?php echo $index; ?>">
                                    <?php echo $faq['question']; ?>
                                </button>
                            </h3>
                            <div id="contactCollapse<?php echo $index; ?>" class="accordion-collapse collapse <?php echo $index === 0 ? 'show' : ''; ?>" aria-labelledby="contactHeading<?php echo $index; ?>" data-bs-parent="#contactFaqAccordion">
                                <div class="accordion-body">
                                    <?php echo $faq['answer']; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</section>
