<?php
/**
 * Contrôleur de base
 * Classe abstraite dont héritent tous les contrôleurs
 */
abstract class BaseController {
    protected $languageManager;
    protected $contentManager;

    /**
     * Constructeur
     *
     * @param LanguageManager $languageManager Gestionnaire de langue
     * @param ContentManager $contentManager Gestionnaire de contenu
     */
    public function __construct($languageManager, $contentManager) {
        $this->languageManager = $languageManager;
        $this->contentManager = $contentManager;
    }

    /**
     * Méthode abstraite à implémenter dans les classes enfants
     *
     * @param array $params Paramètres de la requête
     */
    abstract public function render($params = []);

    /**
     * Charge une vue avec les données fournies
     *
     * @param string $view Nom de la vue
     * @param array $data Données à passer à la vue
     */
    protected function loadView($view, $data = []) {
        // Rendre les données accessibles à la vue
        extract($data);

        // Inclure le template de base
        include ROOT_DIR . '/views/templates/header.php';

        // Inclure la vue demandée
        $viewFile = ROOT_DIR . '/views/pages/' . $view . '.php';
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            // Vue non trouvée
            echo '<div class="container mt-5">';
            echo '<div class="alert alert-danger">Vue non trouvée: ' . $view . '</div>';
            echo '</div>';
        }

        // Inclure le pied de page
        include ROOT_DIR . '/views/templates/footer.php';
    }
}
