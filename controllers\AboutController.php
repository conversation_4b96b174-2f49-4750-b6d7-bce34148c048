<?php
/**
 * Contrôleur de la page À propos
 */
require_once ROOT_DIR . '/controllers/BaseController.php';

class AboutController extends BaseController {
    /**
     * Affiche la page À propos
     * 
     * @param array $params Paramètres de la requête
     */
    public function render($params = []) {
        // Préparer les données pour la vue
        $data = [
            'title' => $this->contentManager->get('global.siteName') . ' - ' . $this->contentManager->get('global.navigation.about'),
            'global' => $this->contentManager->get('global'),
            'content' => $this->contentManager->get('home'),
            'about' => $this->contentManager->get('about'),
            'loans' => $this->contentManager->get('loans'),
            'currentLang' => $this->languageManager->getCurrentLanguage(),
            'availableLangs' => $this->languageManager->getAvailableLanguages()
        ];
        
        // Charger la vue
        $this->loadView('about', $data);
    }
}
