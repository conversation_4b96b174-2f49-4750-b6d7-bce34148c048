<?php
/**
 * Contrôleur de la page de demande de prêt
 */
require_once ROOT_DIR . '/controllers/BaseController.php';
require_once ROOT_DIR . '/includes/mailer/PHPMailer.php';

class ApplyController extends BaseController {
    /**
     * Affiche la page de demande de prêt
     *
     * @param array $params Paramètres de la requête
     */
    public function render($params = []) {
        $message = '';
        $messageType = '';
        $formData = [];

        // Récupérer le type de prêt depuis l'URL ou le formulaire
        $loanType = isset($params[0]) ? $params[0] : (isset($_POST['loan_type']) ? $_POST['loan_type'] : null);

        // Traitement du formulaire de demande
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_form'])) {
            // Récupération des données du formulaire
            $formData = [
                'loan_type' => $_POST['loan_type'] ?? '',
                'amount' => $_POST['amount'] ?? '',
                'duration' => $_POST['duration'] ?? '',
                'first_name' => $_POST['first_name'] ?? '',
                'last_name' => $_POST['last_name'] ?? '',
                'email' => $_POST['email'] ?? '',
                'confirm_email' => $_POST['confirm_email'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'address' => $_POST['address'] ?? '',
                'city' => $_POST['city'] ?? '',
                'zip_code' => $_POST['zip_code'] ?? '',
                'employment_status' => $_POST['employment_status'] ?? '',
                'monthly_income' => $_POST['monthly_income'] ?? '',
                'comments' => $_POST['comments'] ?? ''
            ];

            // Validation des données
            $errors = [];

            if (empty($formData['loan_type'])) {
                $errors[] = $this->contentManager->get('apply.form.loanTypeError'); //'Le type de prêt est requis'
            }

            if (empty($formData['amount']) || !is_numeric($formData['amount'])) {
                $errors[] = $this->contentManager->get('apply.form.amountError'); //'Le montant doit être un nombre valide'
            }

            if (empty($formData['duration']) || !is_numeric($formData['duration'])) {
                $errors[] = $this->contentManager->get('apply.form.durationError'); //'La durée doit être un nombre valide'
            }

            if (empty($formData['first_name'])) {
                $errors[] = $this->contentManager->get('apply.form.firstNameError'); //'Le prénom est requis'
            }

            if (empty($formData['last_name'])) {
                $errors[] = $this->contentManager->get('apply.form.lastNameError'); //'Le nom est requis'
            }

            if (empty($formData['email']) || !filter_var($formData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors[] = $this->contentManager->get('apply.form.emailError'); //'Email invalide';
            }

            if (empty($formData['confirm_email']) || $formData['email'] !== $formData['confirm_email']) {
                $errors[] = $this->contentManager->get('apply.form.confirmEmailError'); //'Les adresses email ne correspondent pas';
            }

            if (empty($formData['phone'])) {
                $errors[] = $this->contentManager->get('apply.form.phoneError'); //'Le téléphone est requis';
            }

            if (empty($errors)) {
                // Créer et envoyer l'email
                $mailer = PHPMailer::createLoanApplicationEmail($formData);
                $emailSent = $mailer->send();

                if ($emailSent) {
                    // Rediriger vers la page de remerciement
                    $redirectUrl = $this->languageManager->getLocalizedUrl('/thank-you/apply');
                    header('Location: ' . $redirectUrl);
                    exit;
                } else {
                    $message = $this->contentManager->get('apply.form.errorMessage'); // 'Une erreur est survenue lors de l\'envoi de votre demande. Veuillez réessayer plus tard.';
                    $messageType = 'danger';
                }
            } else {
                $message = $this->contentManager->get('apply.form.errorsMessage') . '<br>' . implode('<br>', $errors); // 'Veuillez corriger les erreurs suivantes : ' . implode(', ', $errors);
                $messageType = 'danger';
            }
        }

        // Préparer les données pour la vue
        $data = [
            'title' => $this->contentManager->get('global.siteName') . ' - ' . $this->contentManager->get('global.navigation.apply'),
            'global' => $this->contentManager->get('global'),
            'content' => $this->contentManager->get('home'),
            'apply' => $this->contentManager->get('apply'),
            'loans' => $this->contentManager->get('loans'),
            'currentLang' => $this->languageManager->getCurrentLanguage(),
            'availableLangs' => $this->languageManager->getAvailableLanguages(),
            'loanType' => $loanType,
            'message' => $message,
            'messageType' => $messageType,
            'formData' => $formData
        ];

        // Charger la vue
        $this->loadView('apply', $data);
    }
}
