# FinancExpert - Site Web de Prêts

Un site web professionnel et attrayant pour une entreprise de prêts, développé avec PHP, HTML, CSS, Bootstrap et JavaScript.

## Caractéristiques

- **Backend** : PHP sans framework
- **Frontend** : HTML, CSS, Bootstrap, JavaScript
- **Données** : Fichiers JSON (pas de base de données)
- **Multilangue** : Support pour plusieurs langues avec détection automatique
- **Responsive** : Design adapté à tous les appareils
- **Routeur** : Système de routage personnalisé en PHP

## Structure du Projet

```
loanwebsite/
├── assets/
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── main.js
│   └── images/
├── data/
│   └── lang/
│       ├── fr.json
│       ├── en.json
│       └── de.json
├── includes/
│   ├── ContentManager.php
│   ├── LanguageManager.php
│   └── templates/
│       ├── header.php
│       └── footer.php
├── router/
│   ├── Router.php
│   └── controllers/
│       ├── BaseController.php
│       ├── HomeController.php
│       ├── AboutController.php
│       ├── ContactController.php
│       ├── LoansController.php
│       └── ApplyController.php
├── views/
│   ├── home.php
│   ├── about.php
│   ├── contact.php
│   ├── loans.php
│   ├── apply.php
│   └── errors/
│       └── 404.php
├── .htaccess
├── index.php
└── README.md
```

## Installation

1. Clonez ce dépôt dans votre serveur web
2. Assurez-vous que le module de réécriture d'URL (mod_rewrite) est activé sur votre serveur Apache
3. Accédez au site via votre navigateur

## Types de Prêts

Le site présente les types de prêts suivants :
- Prêts personnels
- Prêts immobiliers
- Prêts étudiants
- Prêts automobiles
- Prêts professionnels
- Consolidation de dettes

## Multilangue

Le site supporte plusieurs langues :
- Français (fr)
- Anglais (en)
- Allemand (de)

La langue est détectée automatiquement en fonction des préférences du navigateur, mais peut également être sélectionnée manuellement via l'URL (exemple : example.com/en/about).

## Développement

Pour ajouter une nouvelle langue :
1. Créez un nouveau fichier JSON dans le dossier `data/lang/` (ex: `es.json`)
2. Ajoutez le code de langue dans la constante `AVAILABLE_LANGS` dans `index.php`

Pour ajouter une nouvelle page :
1. Créez un nouveau contrôleur dans `router/controllers/`
2. Créez une nouvelle vue dans `views/`
3. Ajoutez la route dans le tableau `$routes` dans `router/Router.php`
