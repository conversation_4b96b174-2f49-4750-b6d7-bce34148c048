<!-- Hero Section -->
<section class="page-header bg-primary text-white">
    <div class="container">
        <div class="row py-5">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3"><?php echo $loans['title']; ?></h1>
                <p class="lead mb-0"><?php echo $loans['subtitle']; ?></p>
            </div>
        </div>
    </div>
</section>

<!-- Loans Overview Section -->
<section class="py-5">
    <div class="container">
        <div class="row mb-5">
            <div class="col-lg-8">
                <p class="lead"><?php echo $loans['intro']; ?></p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($content['loanTypes'] as $loan): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <picture>
                            <source srcset="/assets/images/<?php echo $loan['image']; ?>.avif" type="image/avif">
                            <source srcset="/assets/images/<?php echo $loan['image']; ?>.webp" type="image/webp">
                            <img src="/assets/images/<?php echo $loan['image']; ?>.jpg" class="card-img-top" alt="<?php echo $loan['title']; ?>" loading="lazy">
                        </picture>
                        <div class="card-body p-4">
                            <h4 class="card-title mb-3"><?php echo $loan['title']; ?></h4>
                            <p class="card-text mb-4"><?php echo $loan['shortDescription']; ?></p>
                            <a href="<?php echo $this->languageManager->getLocalizedUrl('/loans/' . $loan['id']); ?>" class="btn btn-outline-primary">
                                <?php echo $loan['cta']; ?>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-4"><?php echo $content['advantages']['title']; ?></h2>
            </div>
        </div>
        <div class="row">
            <?php foreach ($content['advantages']['items'] as $advantage): ?>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="icon-box mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="#007bff" viewBox="0 0 512 512">
                                    <?php echo $advantage['icon']; ?>
                                </svg>
                            </div>
                            <h4 class="card-title mb-3"><?php echo $advantage['title']; ?></h4>
                            <p class="card-text"><?php echo $advantage['description']; ?></p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Process Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-3"><?php echo $content['process']['title']; ?></h2>
                <p class="lead"><?php echo $content['process']['subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($content['process']['steps'] as $step): ?>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="step-number mb-4">
                                <span class="rounded-circle bg-primary text-white d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                    <?php echo $step['number']; ?>
                                </span>
                            </div>
                            <h4 class="card-title mb-3"><?php echo $step['title']; ?></h4>
                            <p class="card-text"><?php echo $step['description']; ?></p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="section-title mb-3"><?php echo $content['faq']['title']; ?></h2>
                <p class="lead"><?php echo $content['faq']['subtitle']; ?></p>
            </div>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="accordion" id="faqAccordion">
                    <?php 
                    $faqItems = $loans['faq']['items'] ?? $content['faq']['items'];
                    foreach ($faqItems as $index => $faq): 
                    ?>
                        <div class="accordion-item mb-3 border-0 shadow-sm">
                            <h3 class="accordion-header" id="heading<?php echo $index; ?>">
                                <button class="accordion-button <?php echo $index > 0 ? 'collapsed' : ''; ?>" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo $index; ?>" aria-expanded="<?php echo $index === 0 ? 'true' : 'false'; ?>" aria-controls="collapse<?php echo $index; ?>">
                                    <?php echo $faq['question']; ?>
                                </button>
                            </h3>
                            <div id="collapse<?php echo $index; ?>" class="accordion-collapse collapse <?php echo $index === 0 ? 'show' : ''; ?>" aria-labelledby="heading<?php echo $index; ?>" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <?php echo $faq['answer']; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h2 class="mb-3"><?php echo $content['cta']['title']; ?></h2>
                <p class="lead mb-4"><?php echo $content['cta']['subtitle']; ?></p>
                <a href="<?php echo $this->languageManager->getLocalizedUrl('/apply'); ?>" class="btn btn-light btn-lg mb-3">
                    <?php echo $content['cta']['button']; ?>
                </a>
                <p class="mb-0"><?php echo $content['cta']['contact']; ?></p>
            </div>
        </div>
    </div>
</section>
