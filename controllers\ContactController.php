<?php
/**
 * Contrôleur de la page Contact
 */
require_once ROOT_DIR . '/controllers/BaseController.php';
require_once ROOT_DIR . '/includes/mailer/PHPMailer.php';

class ContactController extends BaseController {
    /**
     * Affiche la page Contact
     *
     * @param array $params Paramètres de la requête
     */
    public function render($params = []) {
        $message = '';
        $messageType = '';

        // Traitement du formulaire de contact
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['contact_form'])) {
            // Validation simple des données
            $firstName = trim($_POST['first_name'] ?? '');
            $lastName = trim($_POST['last_name'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $confirmEmail = trim($_POST['confirm_email'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $country = trim($_POST['country'] ?? '');
            $subject = trim($_POST['subject'] ?? '');
            $message_content = trim($_POST['message'] ?? '');

            $errors = [];

            if (empty($firstName)) {
                $errors[] = $this->contentManager->get('contact.form.firstNameError');
            }

            if (empty($lastName)) {
                $errors[] = $this->contentManager->get('contact.form.lastNameError');
            }

            if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errors[] = $this->contentManager->get('contact.form.emailError');
            }

            if (empty($confirmEmail) || $email !== $confirmEmail) {
                $errors[] = $this->contentManager->get('contact.form.confirmEmailError');
            }

            if (empty($country)) {
                $errors[] = $this->contentManager->get('contact.form.countryError');
            }

            if (empty($subject)) {
                $errors[] = $this->contentManager->get('contact.form.subjectError');
            }

            if (empty($message_content)) {
                $errors[] = $this->contentManager->get('contact.form.messageError');
            }

            if (empty($errors)) {
                // Préparer les données pour l'email
                $formData = [
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'email' => $email,
                    'phone' => $phone,
                    'country' => $country,
                    'subject' => $subject,
                    'message' => $message_content
                ];

                // Créer et envoyer l'email
                $mailer = PHPMailer::createContactEmail($formData);
                $emailSent = $mailer->send();

                if ($emailSent) {
                    // Rediriger vers la page de remerciement
                    $redirectUrl = $this->languageManager->getLocalizedUrl('/thank-you/contact');
                    header('Location: ' . $redirectUrl);
                    exit;
                } else {
                    $message = 'Une erreur est survenue lors de l\'envoi du message. Veuillez réessayer plus tard.';
                    $messageType = 'danger';
                }
            } else {
                $message = 'Veuillez corriger les erreurs suivantes : ' . implode(', ', $errors);
                $messageType = 'danger';
            }
        }

        // Préparer les données pour la vue
        $data = [
            'title' => $this->contentManager->get('global.siteName') . ' - ' . $this->contentManager->get('global.navigation.contact'),
            'contact' => $this->contentManager->get('contact'),
            'global' => $this->contentManager->get('global'),
            'content' => $this->contentManager->get('home'),
            'loans' => $this->contentManager->get('loans'),
            'currentLang' => $this->languageManager->getCurrentLanguage(),
            'availableLangs' => $this->languageManager->getAvailableLanguages(),
            'message' => $message,
            'messageType' => $messageType
        ];

        // Charger la vue
        $this->loadView('contact', $data);
    }
}
